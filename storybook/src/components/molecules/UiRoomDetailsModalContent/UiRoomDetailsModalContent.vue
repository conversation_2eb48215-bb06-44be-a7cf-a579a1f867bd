<script setup lang="ts">
import UiAccordionGroup from "../../molecules/UiAccordionGroup/UiAccordionGroup.vue"
import UiHotelHeading from "../../molecules/UiHotelHeading/UiHotelHeading.vue"
import UiImage from "../../atoms/UiImage/UiImage.vue"
import { UiImageLoading } from "../../atoms/UiImage/enums"
import UiListItemGroup from "../../molecules/UiListItemGroup/UiListItemGroup.vue"
import type { UiRoomDetailsModalContentProps } from "./interface"
import UiSlider from "../../molecules/UiSlider/UiSlider.vue"
import { computed, onMounted, onUnmounted } from "vue"

const props = defineProps<UiRoomDetailsModalContentProps>()

const hasSingleImage = computed(() => props.images.length === 1)

const formattedAmenities = computed(() =>
  props.amenities.map((amenity) => ({
    id: amenity.id,
    name: amenity.name,
    title: amenity.title
  }))
)

// Fonction pour gérer les clics sur les accordéons
const handleAccordionClick = (event: Event) => {
  const target = event.target as HTMLElement
  const accordion = target.closest(".ads-accordion")

  if (accordion) {
    // Chercher le bouton dans cet accordéon
    const button = accordion.querySelector(".ads-button") as HTMLButtonElement

    // Si on a cliqué sur le bouton lui-même, ne rien faire (laisser le comportement normal)
    if (target.closest(".ads-button")) {
      return
    }

    // Sinon, déclencher le clic sur le bouton
    if (button) {
      button.click()
    }
  }
}

onMounted(() => {
  // Ajouter l'écouteur d'événement sur le document
  document.addEventListener("click", handleAccordionClick)
})

onUnmounted(() => {
  // Nettoyer l'écouteur d'événement
  document.removeEventListener("click", handleAccordionClick)
})
</script>

<template>
  <article class="Room-details-modal-content">
    <UiImage
      v-if="hasSingleImage"
      class="Room-details-modal-content__image"
      v-bind="images[0]"
      :loading="UiImageLoading.EAGER"
    />

    <UiSlider v-else :images="images" />

    <div class="Room-details-modal-content__content">
      <UiHotelHeading class="Room-details-modal-content__header" :description="description" :title="title" />

      <ul class="Room-details-modal-content__key-features">
        <li v-for="keyFeature in keyFeatures" :key="keyFeature.label">
          <p class="Room-details-modal-content__description">{{ keyFeature.description }}</p>
          <p class="Room-details-modal-content__label">{{ keyFeature.label }}</p>
        </li>
      </ul>
    </div>

    <!-- We're mapping amenities to avoid sending more props than necessary (it'll appear in the DOM) -->
    <UiAccordionGroup
      v-if="amenities && amenities.length > 0"
      class="Room-details-modal-content__accordion"
      :items="formattedAmenities"
    >
      <template v-for="amenity in amenities" :key="amenity.id" #[`content-${amenity.name}`]>
        <UiListItemGroup v-for="item in amenity.items" :key="item.label" :items="item.items" :title="item.label" />
      </template>
    </UiAccordionGroup>
  </article>
</template>

<style lang="scss" scoped>
@use "./UiRoomDetailsModalContent.scss";
</style>
